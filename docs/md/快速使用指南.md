# NexusRecv 快速使用指南

## 快速开始

### 1. 获取程序

本项目目前需要通过源码构建获取可执行文件：

```bash
# 1. 克隆项目
git clone https://github.com/your-repo/nexusrecv.git
cd nexusrecv

# 2. 安装构建依赖
pip install -r scripts/build/requirements_build.txt

# 3. 构建可执行文件
python scripts/build/build_by_platform.py
```

构建完成后：
- **Windows用户**: 可执行文件位于 `release/windows/NexusRecv.exe`
- **Linux用户**: 可执行文件位于 `release/linux/NexusRecv/` 目录，或使用生成的deb包

### 2. 运行程序

#### Windows用户
1. 双击 `NexusRecv.exe` 运行
2. 首次运行会打开配置选择界面

#### Linux用户
```bash
# 使用deb包安装
sudo dpkg -i release/nexusrecv-webhook-server_1.0.2_amd64.deb
nexusrecv-webhook-server

# 或直接运行
cd release/linux/NexusRecv
./NexusRecv
```

### 3. 配置服务器

#### 首次运行
1. 第一次运行程序时，由于没有任何配置记录，会自动进入主界面
2. 在主界面中点击菜单栏的"设置"

#### 配置服务器参数
在设置菜单中需要配置两个部分：

**1. 服务端配置**：
- **API密钥**：自定义密钥，用于API访问认证
- **服务端运行时间**：设置服务器每日的运行时间段

**2. 发信设备标识**：
- 添加需要接收数据的设备标识和描述
- 例如：设备标识 `device001`，描述 `我的测试设备`

#### 后续运行
1. 配置保存后，下次启动程序会显示配置选择界面
2. 选择已有的有效配置即可进入主界面
3. 如需修改配置，同样在主界面的"设置"菜单中操作

### 4. 启动服务

#### 启动服务器
1. 完成配置后，在主界面点击"启动服务器"按钮
2. 服务器启动成功后，界面会显示"服务器运行中"状态
3. 可以看到服务器的IP地址和端口信息

#### 服务器状态监控
- 实时数据会在主界面的表格中显示
- 可以查看服务器运行时长、内存使用等信息
- 支持启动/停止服务器操作

## 基本使用

### 1. 发送数据

使用任何支持HTTP的设备或程序发送数据：

```bash
# 使用curl发送数据（示例）
curl -X POST http://服务器IP:端口/webhook/save \
  -H "Content-Type: application/json" \
  -H "X-Client-Key: 设备标识" \
  -d '{"content":"数据内容"}'

# 实际示例
curl -X POST http://*************:8000/webhook/save \
  -H "Content-Type: application/json" \
  -H "X-Client-Key: device001" \
  -d '{"content":"温度:25.6°C"}'
```

**注意**：
- 设备标识必须在配置中预先添加
- 数据内容长度限制在1-100字符
- 服务器IP和端口以实际配置为准

### 2. 查看数据

#### GUI界面查看
- 服务器启动后，接收到的数据会实时显示在主界面的表格中
- 双击表格行可复制完整数据内容
- 可以按设备标识筛选数据
- 支持数据搜索功能

### 3. 系统托盘

- 点击窗口关闭按钮会最小化到系统托盘
- 右键托盘图标可以：
  - 显示/隐藏主窗口
  - 启动/停止服务器
  - 退出程序

## 常用功能

### 1. 多配置管理

- 支持创建多个配置文件
- 每个配置文件可以独立运行
- 配置文件自动保存在用户目录的`.webhook_server`文件夹

### 2. 数据管理

- 数据自动保存到SQLite数据库
- 支持设置数据过期时间（天数）
- 支持设置数据存储上限（条数）
- 过期和超量数据会自动清理

### 3. 日志查看

- 日志文件保存在`logs`目录
- 支持不同级别的日志记录
- 日志文件按日期自动分割

## 网络配置

### 1. 防火墙设置

#### Windows
- 首次运行时Windows会询问是否允许网络访问
- 选择"允许访问"即可

#### Linux
```bash
# 开放端口（以8000为例）
sudo ufw allow 8000/tcp
```

### 2. 局域网访问

- 确保服务器配置中`host = 0.0.0.0`
- 其他设备使用服务器IP地址访问
- 例如：`http://*************:8000/webhook/save`

### 3. IP白名单

```ini
# 允许所有IP
whitelist = *

# 允许特定IP
whitelist = *************,*************

# 允许网段
whitelist = ***********/24

# 混合配置
whitelist = ***********/24,********,127.0.0.1
```

## 故障排除

### 1. 服务无法启动

**检查端口占用**
```bash
# Windows
netstat -an | findstr :8000

# Linux
netstat -tuln | grep :8000
```

**解决方法**
- 更改配置文件中的端口号
- 或者停止占用端口的其他程序

### 2. 设备无法发送数据

**检查设备标识**
- 确保设备标识在配置文件的`[client_info]`中存在
- 设备标识区分大小写
- 设备标识只能包含字母和数字

**检查网络连接**
- 确保设备和服务器在同一网络
- 检查防火墙设置
- 测试网络连通性：`ping 服务器IP`

### 3. GUI界面问题

**界面无响应**
- 检查配置文件格式是否正确
- 查看日志文件中的错误信息
- 重启程序

**系统托盘不显示**
- 检查系统托盘设置
- 确保系统支持托盘功能

## 高级配置

### 1. 定时运行

```ini
[server]
# 每天7点到23点运行
run_time = 07:00-23:00

# 24小时运行
run_time = 00:00-00:00
```

### 2. 时区设置

```ini
[server]
# 设置时区
time_zone = Asia/Shanghai
```

### 3. 数据清理策略

```ini
[server]
# 数据保留7天
expire_data_days = 7

# 最多保存10万条数据
data_limit_num = 100000

# 永不过期（设置为0或负数）
expire_data_days = 0

# 不限制数量（设置为0或负数）
data_limit_num = 0
```

## 技术支持

### 程序文件说明

#### Windows版本
- `NexusRecv.exe` - 主程序可执行文件
- 程序运行时会自动在用户目录创建 `.webhook_server` 文件夹
- 配置文件和数据库文件都保存在此文件夹中

#### Linux版本
- **deb包安装**: 程序安装到系统目录，命令为`nexusrecv-webhook-server`
- **直接运行**: 使用构建生成的`NexusRecv`可执行文件
- 配置和数据文件同样保存在用户目录的 `.webhook_server` 文件夹

### 联系方式
- 邮箱：<EMAIL>

### 常见问题
1. **Q: 可以同时运行多个实例吗？**
   A: 可以，但需要使用不同的配置文件和端口。

2. **Q: 数据存储在哪里？**
   A: 数据存储在用户目录的`.webhook_server/config_data.db`文件中。

3. **Q: 如何备份数据？**
   A: 复制`.webhook_server`整个文件夹即可。

4. **Q: 支持HTTPS吗？**
   A: 目前不直接支持，建议使用nginx等反向代理实现HTTPS。

5. **Q: Linux下提示权限不足怎么办？**
   A: 给程序文件添加执行权限：`chmod +x NexusRecv` 或 `chmod +x start.sh`

6. **Q: Windows下被杀毒软件拦截怎么办？**
   A: 将程序添加到杀毒软件的白名单中，或临时关闭实时保护。

---

**版本**: v1.0.2  
**更新日期**: 2025-09-03  
**适用软件**: NexusRecv 多类别设备数据接收存储服务端