--config D:\Git\python-samples-hub\tests\other_conf\ok\server_config_3.ini

D:\Git\python-samples-hub\src\webhook_server\webhook_server_gui.py --config D:\Git\python-samples-hub\tests\other_conf\ok\server_config_3.ini 

D:\Git\python-samples-hub\src\webhook_server\config_selection_gui.py 

D:\Python\Python311\python.exe D:\Git\python-samples-hub\src\webhook_server\webhook_server_gui.py --config D:\Git\python-samples-hub\tests\other_conf\ok\server_config_3.ini 

src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent) # noqa

# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
    sys.path.remove(src_path)
sys.path.insert(0,src_path)


文档doc更新 --- doc目录下文档更新说明，其中内容需要根据最新当前项目代码进行更新，并指明其是用于webhook_server项目


将 专业文件中cron中的read.py --- 分析获取短信验证码 功能实现到common中的utils中
删除 git\1 目录

定时任务脚本中需要补充，只有存在时才执行
cron目录统一完成之后，补充什么时候执行定时任务 --- 补充定时任务机制

python downloadBili.py --req-cookie ../conf/bili_cookies.txt --ytdlp-cookie ../conf/bili_Netscape.txt --media-id 3179004075
python downloadBili.py --file urls.txt --out ./downloads --workers 4
