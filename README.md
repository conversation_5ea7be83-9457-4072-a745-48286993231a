# NexusRecv - 多类别设备数据接收存储桌面应用

> 本项目是一个基于FastAPI和ttkbootstrap的桌面GUI应用程序，支持局域网内多种设备的实时数据接收、存储和分发。

## 项目简介

NexusRecv是一个专业的桌面设备数据接收应用程序，可以用于接收并存储多种类型设备的实时数据并分发给指定的第三方使用。主要功能包括：

- **数据接收**：通过RESTful API接收来自局域网内各种设备的数据
- **身份验证**：支持基于客户端标识的设备认证和API密钥验证
- **数据存储**：使用SQLite数据库存储接收到的数据，支持自动清理过期数据
- **实时监控**：提供现代化图形界面实时显示接收到的数据和系统状态
- **多进程支持**：支持GUI界面、命令行、配置选择三种运行模式，可同时运行多个实例
- **配置管理**：支持多配置文件管理，防止配置冲突，支持跨进程配置同步
- **系统托盘**：支持系统托盘运行，提供便捷的系统集成
- **跨平台构建**：支持Windows exe和Linux deb包构建，便于分发

## 项目结构

```
src/
├── common/                              # 通用模块
│   ├── constants/         # 通用常量定义
│   │   ├── const.py       # 基础常量
│   │   └── gui_const.py   # GUI常量
│   ├── models/            # 通用数据模型
│   │   ├── base_config_unique_manage.py # 配置唯一性管理
│   │   ├── gui_widgets.py # GUI组件模型
│   │   ├── single_instance_meta.py # 单例元类
│   │   ├── sql_log.py     # SQL日志模型
│   │   └── sqlite_base_manager.py # SQLite基础管理器
│   └── utils/             # 通用工具函数
│       ├── async_utils.py # 异步工具
│       ├── common_utils.py # 通用工具
│       ├── config_utils.py # 配置工具
│       ├── file_utils.py  # 文件工具
│       ├── gui_utils.py   # GUI工具
│       ├── logging_utils.py # 日志工具
│       ├── network_utils.py # 网络工具
│       ├── process_utils.py # 进程工具
│       ├── system_utils.py # 系统工具
│       └── time_utils.py  # 时间工具
├── webhook_server/                      # Webhook服务器模块
│   ├── config/            # 配置相关模块
│   │   ├── constants.py   # 项目常量定义
│   │   ├── gui_constants.py # GUI界面常量
│   │   └── config_check.py # 配置验证模块
│   ├── models/            # 核心业务模型
│   │   ├── webhook_server.py # Webhook服务器核心类
│   │   ├── server_data.py # 数据传输模型
│   │   ├── server_properties.py # 服务器属性管理
│   │   ├── server_state.py # 服务器状态管理
│   │   ├── gui_properties.py # GUI属性管理
│   │   ├── gui_server_info.py # GUI服务器信息模型
│   │   ├── server_data_manager.py # 数据管理器
│   │   └── child_webhook_server.py # 子进程服务器
│   ├── utils/             # 工具函数模块
│   │   ├── config_lock.py # 配置锁管理
│   │   ├── webhook_gui_utils.py # GUI工具函数
│   │   └── webhook_server_utils.py # 服务器工具函数
│   ├── gui/               # GUI界面模块
│   │   ├── main_window.py # 主界面控制器
│   │   ├── data_table.py  # 数据表格组件
│   │   ├── status_monitor.py # 状态监控组件
│   │   ├── server_control.py # 服务器控制组件
│   │   ├── system_tray.py # 系统托盘组件
│   │   ├── menu_dialogs/  # 菜单对话框
│   │   └── other_main_gui/ # 其他GUI组件
│   ├── webhook_server_gui.py # GUI主界面启动
│   ├── webhook_server_command.py # 命令行启动模块
│   └── config_selection_gui.py # 配置选择界面
├── tests/                # 测试模块
│   ├── project_tests/    # 项目测试
│   ├── other_conf/       # 测试配置
│   └── deprecated/       # 废弃过期代码文件
├── resources/            # 资源文件
│   ├── icons/           # 图标文件
│   ├── server_config.ini # 样例服务端配置
│   └── log.ini          # 样例日志配置
├── docs/                # 项目文档
├── logs/                # 日志文件目录
└── release/             # 发布文件
    └── windows/         # Windows发布版本
```

## 快速开始

### 环境要求
- **Python版本**: 3.11+
- **操作系统**: Windows 10/11, Linux (Ubuntu/CentOS), macOS
- **硬件要求**: CPU 2核+, 内存 4GB+, 磁盘 10GB+
- **网络环境**: 局域网环境，支持多设备通信

### 依赖安装
```bash
pip install -r requirements.txt
```

### 运行方式

#### 1. 源码运行（开发环境）
```bash
# 配置选择界面启动（推荐）
python src/webhook_server/config_selection_gui.py

# 直接启动GUI界面
python src/webhook_server/webhook_server_gui.py --config <配置文件路径>

# 命令行模式启动服务器
python src/webhook_server/webhook_server_command.py --config <配置文件路径>
```

#### 2. 可执行文件运行（用户环境）
```bash
# 构建可执行文件
python scripts/build/build_by_platform.py

# Windows - 运行构建后的程序
release/windows/NexusRecv.exe

# Linux - 运行构建后的程序
release/linux/NexusRecv/NexusRecv

# 或安装deb包后运行
sudo dpkg -i release/nexusrecv-webhook-server_1.0.2_amd64.deb
nexusrecv-webhook-server
```

## API接口说明

NexusRecv提供RESTful API接口，支持设备数据的接收、存储和查询。所有接口基于HTTP协议，使用JSON格式进行数据交换。

### 基础信息
- **基础URL**: `http://{host}:{port}`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token / Client Key Header

### 接口概览

| 接口 | 方法 | 功能 | 认证方式 | IP白名单检查 |
|------|------|------|----------|-------------|
| `/webhook/token` | GET | 获取访问令牌 | Bearer Token (API Key) | 是 |
| `/webhook/save` | POST | 保存设备数据 | X-Client-Key Header | 否 |
| `/webhook/unread` | GET | 获取未读数据 | Bearer Token | 是 |

### 1. 获取访问令牌
**接口**: `GET /webhook/token`

**请求示例**:
```bash
curl -H "Authorization: Bearer your_api_key_here" \
     http://localhost:8000/webhook/token
```

**响应示例**:
```json
{
  "token": "server_refresh_token_string"
}
```

### 2. 保存设备数据
**接口**: `POST /webhook/save`

**请求示例**:
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-Client-Key: device001" \
  -d '{"content":"温度:25.6°C,湿度:60%"}' \
  http://localhost:8000/webhook/save
```

**响应示例**:
```json
{
  "status": "success",
  "message_id": "01HZXYZ123456789ABCDEF"
}
```

**参数说明**:
- `X-Client-Key`: 设备标识，必须在GUI中预先配置
- `content`: 数据内容，长度限制1-100字符

### 3. 获取未读数据
**接口**: `GET /webhook/unread`

**请求示例**:
```bash
curl -H "Authorization: Bearer your_token_here" \
     "http://localhost:8000/webhook/unread?size=10&client_key=device001"
```

**响应示例**:
```json
{
  "messages": [
    {
      "id": "01HZXYZ123456789ABCDEF",
      "message": "温度:25.6°C,湿度:60%",
      "client_key": "device001",
      "reception_time": "2025-09-03 10:30:45"
    }
  ]
}
```

**参数说明**:
- `size`: 返回数据数量，必填，必须大于0
- `client_key`: 设备标识，可选
- `minutes`: 查询时间范围（分钟），可选

### API调用示例

#### Windows CMD示例
```cmd
# 获取令牌
curl -X GET ^
  "http://*************:8000/webhook/token" ^
  -H "Authorization: Bearer your_api_key_here"

# 发送数据
curl -X POST ^
  "http://*************:8000/webhook/save" ^
  -H "Content-Type: application/json" ^
  -H "X-Client-Key: device001" ^
  -d "{\"content\":\"温度:25.6°C,湿度:60%\"}"

# 获取未读数据
curl -X GET ^
  "http://*************:8000/webhook/unread?size=10&client_key=device001" ^
  -H "Authorization: Bearer your_token_here"
```

#### Linux/macOS示例
```bash
# 获取令牌
curl -H "Authorization: Bearer your_api_key_here" \
     http://*************:8000/webhook/token

# 发送数据
curl -X POST \
     -H "Content-Type: application/json" \
     -H "X-Client-Key: device001" \
     -d '{"content":"温度:25.6°C,湿度:60%"}' \
     http://*************:8000/webhook/save

# 获取未读数据
curl -H "Authorization: Bearer your_token_here" \
     "http://*************:8000/webhook/unread?size=10&client_key=device001"
```

## 配置文件说明

NexusRecv使用INI格式的配置文件，支持多配置文件管理和跨进程配置同步。

### 配置文件结构

```ini
[server]
# API访问密钥，用于获取访问令牌
api_key = your_api_key_here
# IP白名单，支持单个IP、网段或*（允许所有IP）
whitelist = ***********/24,127.0.0.1,*
# 服务器监听地址，建议使用0.0.0.0
host = 0.0.0.0
# 服务器监听端口
port = 8000
# 数据库表名（多进程唯一）
message_data_table_name = message_data_1
# 日志配置文件路径
log_config_path = /path/to/log_config.ini
# 服务器运行时间段，格式为HH:MM-HH:MM，相同时间表示全天运行
run_time = 07:00-23:00
# 时区设置
time_zone = Asia/Shanghai
# 数据过期保存天数，非正数表示永不过期
expire_data_days = 7
# 数据存储上限数量，非正数表示不限制
data_limit_num = 100000
# 应用名称（多进程唯一，用作日志文件名前缀）
app_name = webhook_server_1
# 是否启用SQL日志记录
enable_sql_logging = false

[client_info]
# 设备标识和描述的映射关系
device001 = 温湿度传感器1号
device002 = 温湿度传感器2号
sensor001 = 压力传感器
```

### 配置项详细说明

#### [server] 节点配置项

| 配置项 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `api_key` | string | 是 | API访问密钥，用于获取访问令牌 |
| `whitelist` | string | 是 | IP白名单，多个IP用逗号分隔 |
| `host` | string | 是 | 服务器监听地址 |
| `port` | int | 是 | 服务器监听端口 |
| `message_data_table_name` | string | 是 | 数据库表名，多进程间必须唯一 |
| `log_config_path` | string | 是 | 日志配置文件路径 |
| `run_time` | string | 是 | 运行时间段，格式HH:MM-HH:MM |
| `time_zone` | string | 是 | 时区设置 |
| `expire_data_days` | int | 是 | 数据过期天数 |
| `data_limit_num` | int | 是 | 数据存储上限 |
| `app_name` | string | 是 | 应用名称 |
| `enable_sql_logging` | bool | 是 | 是否启用SQL日志 |

#### [client_info] 节点配置项
存储发信设备的标识信息，格式为：
```ini
[client_info]
设备标识1 = 设备描述1
设备标识2 = 设备描述2
```

**设备标识要求**:
- 长度：10-30个字符
- 字符集：仅支持字母和数字
- 大小写敏感

## 功能特性

### 核心功能
- **多设备支持**: 支持同时接收多种类型设备的数据，基于设备标识进行分类管理
- **实时数据处理**: 基于FastAPI的高性能异步数据处理，毫秒级响应时间
- **数据持久化**: 使用SQLite数据库安全存储数据，支持ULID格式的唯一消息ID
- **智能数据管理**: 自动清理过期数据和超量数据，支持可配置的清理策略
- **多实例运行**: 支持多配置文件并发运行，防止配置冲突和资源竞争

### 用户界面
- **现代化GUI**: 基于ttkbootstrap的现代化界面设计，支持主题切换
- **实时监控**: 实时显示接收数据、系统状态和性能指标
- **配置管理**: 可视化配置文件管理，支持配置验证和错误提示
- **系统托盘**: 支持最小化到系统托盘，提供便捷操作和后台运行
- **数据表格**: 支持双击复制数据，提供数据筛选和搜索功能

### 安全认证
- **多层认证**: API密钥认证、设备标识认证、令牌认证三重保护
- **IP白名单**: 支持单个IP、网段白名单和通配符配置
- **设备管理**: 基于客户端标识的设备认证机制，支持设备描述映射
- **数据验证**: 严格的数据格式验证和长度限制

### 系统特性
- **跨平台**: 支持Windows、Linux、macOS系统，提供原生可执行文件
- **桌面应用**: 专为桌面环境设计的GUI应用，用户友好的交互体验
- **定时任务**: 支持定时运行和自动化任务调度，时区感知的时间计算
- **日志系统**: 完整的日志记录和管理系统，支持多级日志和文件轮转
- **性能监控**: 实时CPU、内存使用率监控，系统资源统计

## 构建和打包

### 开发环境搭建

1. **环境要求**
   - Python 3.11+
   - 支持Windows、Linux、macOS系统
   - 需要安装requirements.txt中的所有依赖

2. **代码结构规范**
   - 使用pytest进行单元测试，不使用unittest
   - 遵循单例模式，防止多实例冲突
   - 支持多进程运行，使用SQLite进行进程间配置同步
   - 在包含sys.path.append(src_path)的类中不能使用相对导入

3. **测试环境搭建**
```python
import logging
import sys
from pathlib import Path

src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent)
# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
   sys.path.remove(src_path)
sys.path.insert(0,src_path)

logger = logging.getLogger(__name__)
```

### 构建可执行文件

#### 跨平台自动构建（推荐）
```bash
# 安装构建依赖
pip install -r scripts/build/requirements_build.txt

# 自动检测平台并构建
python scripts/build/build_by_platform.py
```

#### 平台特定构建
```bash
# Windows平台
python scripts/build/build_windows.py

# Linux平台（包含deb包）
python scripts/build/build_linux.py
```

**构建输出**:
- **Windows**: `release/windows/NexusRecv.exe`
- **Linux**: `release/linux/NexusRecv/` 和 deb安装包

## 使用指南

### 网络环境要求
- **局域网使用**: 发信设备必须与运行程序的计算机处于同一局域网
- **防火墙配置**: 确保防火墙允许程序的网络通信
- **Windows防火墙**: 首次运行可能需要允许防火墙访问

### 服务端运行注意事项
- **配置锁定**: 服务端运行期间不能修改配置项
- **数据操作**: 双击实时数据表格中的行可复制完整数据内容
- **定时运行**: 支持配置每日运行时间段
- **自动清理**: 系统会自动清理过期数据和超量数据
- **多实例**: 支持多配置文件同时运行，但同一配置文件不能被多个实例使用

### 设备接入要求
- **设备标识**: 长度10-30个字符，仅支持字母和数字，大小写敏感
- **请求头**: 必须在Header中包含`X-Client-Key`字段
- **数据内容**: 长度限制在1-100个字符之间
- **网络协议**: 使用HTTP/HTTPS协议，支持keep-alive连接

### 故障排除

#### 常见问题
1. **网络连接问题**
   - 使用GUI界面的"网络通信修复"功能
   - 检查防火墙设置和端口占用
   - 验证IP白名单配置

2. **配置文件问题**
   - 确保配置文件使用UTF-8编码
   - 检查配置项格式和必填项
   - 验证时区和时间格式设置

3. **数据接收问题**
   - 检查设备标识是否在client_info中配置
   - 验证API密钥和令牌是否正确
   - 查看日志文件获取详细错误信息

#### 日志分析
- **日志位置**: 默认在logs目录下
- **日志级别**: 支持DEBUG、INFO、WARNING、ERROR、CRITICAL
- **日志轮转**: 支持按日期和大小自动轮转
- **彩色输出**: 控制台支持彩色日志输出

### 性能优化建议

#### 数据管理
- **定期清理**: 建议定期清理过期数据，避免数据库过大
- **存储限制**: 合理设置数据存储上限，防止磁盘空间不足
- **索引优化**: 数据库自动创建必要索引，提升查询性能

#### 系统资源
- **内存使用**: 监控内存使用情况，避免内存泄漏
- **CPU负载**: 在高并发场景下监控CPU使用率
- **网络带宽**: 合理控制并发连接数和数据传输频率

#### 并发处理
- **连接池**: 数据库连接池自动管理，无需手动配置
- **异步处理**: 基于FastAPI的异步处理，支持高并发
- **负载均衡**: 支持多实例部署，实现负载分担

## 测试说明

NexusRecv使用pytest进行单元测试和集成测试，确保代码质量和系统稳定性。

### 测试环境配置
```python
import logging
import os
import sys
from pathlib import Path

# 测试环境路径配置
src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

logger = logging.getLogger(__name__)
```

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_webhook_server.py

# 运行测试并显示详细输出
pytest -v

# 运行测试并生成覆盖率报告
pytest --cov=src tests/

# 运行特定标记的测试
pytest -m "not slow"
```

### 测试分类
- **单元测试**: 测试单个函数和类的功能
- **集成测试**: 测试模块间的交互
- **API测试**: 测试HTTP接口的功能
- **GUI测试**: 测试图形界面的功能
- **性能测试**: 测试系统性能和并发能力

## 技术架构

### 技术栈
- **后端框架**: FastAPI + Uvicorn (高性能异步Web框架)
- **数据库**: SQLite (轻量级嵌入式数据库)
- **GUI框架**: tkinter + ttkbootstrap (现代化桌面界面)
- **任务调度**: APScheduler (定时任务管理)
- **数据验证**: Pydantic (数据模型验证)
- **进程管理**: multiprocessing + psutil (多进程和系统监控)
- **网络处理**: aiohttp + requests (HTTP客户端和服务端)
- **日志系统**: logging + colorama (彩色日志输出)
- **系统托盘**: pystray (系统托盘集成)
- **打包工具**: PyInstaller (可执行文件打包)

### 架构设计
- **分层架构**: 清晰的模块分层，便于维护和扩展
- **单例模式**: 防止多实例冲突，确保资源唯一性
- **异步处理**: 基于asyncio的高性能异步处理
- **事件驱动**: 基于事件的松耦合架构设计
- **配置管理**: 集中化配置管理，支持热更新
- **日志系统**: 统一的日志管理和分析系统

## 项目信息

### 版本信息
- **项目名称**: NexusRecv
- **当前版本**: 1.0.2
- **发布日期**: 2025-09-03
- **开发语言**: Python 3.11+
- **许可协议**: Apache License 2.0

### 联系方式
- **邮箱**: <EMAIL>

### 贡献指南

欢迎提交Issue和Pull Request来改进项目。在提交代码前，请确保：

1. **代码规范**: 遵循PEP 8编码规范
2. **测试覆盖**: 添加必要的测试用例，确保测试覆盖率
3. **文档更新**: 更新相关文档和注释
4. **测试通过**: 确保所有现有测试通过
5. **功能完整**: 确保新功能完整可用

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 更新日志

### v1.0.2 (2025-09-03)
- 新增系统托盘功能，支持最小化到托盘
- 优化GUI界面性能和用户体验
- 增强配置文件验证和错误提示
- 修复多进程配置同步问题
- 完善API接口和文档
- 支持跨平台打包（Windows/Linux/macOS）

### v1.0.1 (2025-06-01)
- 初始版本发布
- 支持多设备数据接收和存储
- 提供GUI和命令行两种运行模式
- 实现配置管理和多实例支持
- 基础API接口实现

### 许可证

本软件遵循Apache License 2.0开源协议发布。您可以自由使用、修改和分发本软件，但需要遵守相关法律法规和协议使用条款。

---

**感谢使用NexusRecv！如有问题或建议，欢迎通过Issue或邮件联系我们。**
